package com.gofar.user_service.entity;


import jakarta.persistence.Embeddable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Embeddable
@Data
@NoArgsConstructor
@AllArgsConstructor
public class EnergyPreference {

    private boolean priorityEco;
    private boolean EVCharging;
    private PreferredTime preferredTime;

    @Getter
    public enum PreferredTime {
        EARLY_MORNING("06:00-08:00"),
        MORNING("08:00-12:00"),
        AFTERNOON("12:00-16:00"),
        EVENING("16:00-20:00"),
        NIGHT("20:00-23:00");

        private final String timeRange;

        PreferredTime(String timeRange) {
            this.timeRange = timeRange;
        }

    }
}
